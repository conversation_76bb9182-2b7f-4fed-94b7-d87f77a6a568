package com.mlc.workflow.core.editor.structure.command;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.utils.GatewaySemanticsStrategy;
import com.mlc.workflow.core.editor.structure.utils.ValidationUtils;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * 网关操作命令
 * 实现网关的新增、删除、类型切换等操作
 *
 * 根据设计方案实现：
 * - 网关新增（左侧放置/不移动策略）
 * - 网关删除（扁平化处理）
 * - 网关类型切换（并行↔唯一）
 * - EndOwner规则维护
 */
@Slf4j
public class GatewayOperations {

    private final AutoWireStrategy autoWireStrategy;
    private final NodeBatchExecutor nodeBatchExecutor;

    public GatewayOperations(NodeBatchExecutor nodeBatchExecutor, AutoWireStrategy autoWireStrategy) {
        this.autoWireStrategy = autoWireStrategy;
        this.nodeBatchExecutor = nodeBatchExecutor;
    }
    
    /**
     * 放置策略枚举
     */
    public enum PlacementStrategy {
        LEFT_PLACEMENT,  // 左侧放置（移动原有链段到左分支）
        NO_MOVE         // 不移动（在原位置插入网关）
    }
    
    /**
     * 新增网关（默认并行）
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(String atNodeId, PlacementStrategy placement) {
        return addGateway(atNodeId, placement, GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL);
    }

    /**
     * 新增网关
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @param gatewayType 网关类型
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(String atNodeId,
                                PlacementStrategy placement, Integer gatewayType) {
        // 使用统一的参数校验
        ValidationUtils.validateGatewayOperationParams(atNodeId, "atNodeId");
        ValidationUtils.validateExecutorState(nodeBatchExecutor);
        ValidationUtils.validateGatewayType(gatewayType);

        // 从工作副本获取节点
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas atNode = ValidationUtils.requireNodeExists(workingCopy, atNodeId, "插入点");
        IRoutable routableAtNode = ValidationUtils.requireRoutable(atNode, "插入点节点");

        String originalNextId = routableAtNode.getNextId();

        // 创建网关和分支叶子
        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setName("网关");
        gateway.setGatewayType(gatewayType); // 设置网关类型

        ConditionNodeCanvas leftBranch = new ConditionNodeCanvas();
        leftBranch.setName("分支1");

        ConditionNodeCanvas rightBranch = new ConditionNodeCanvas();
        rightBranch.setName("分支2");

        // 如果是唯一分支网关，为分支叶子设置默认条件
        if (gatewayType == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
            setDefaultConditionForBranch(leftBranch, 0);
            setDefaultConditionForBranch(rightBranch, 1);
        }

        // 设置网关的分支
        gateway.getFlowIds().add(leftBranch.getId());
        gateway.getFlowIds().add(rightBranch.getId());

        // 设置分支叶子的前驱
        leftBranch.setPrveId(gateway.getId());
        rightBranch.setPrveId(gateway.getId());

        nodeBatchExecutor.createNode(gateway);
        nodeBatchExecutor.createNode(leftBranch);
        nodeBatchExecutor.createNode(rightBranch);

        // 根据放置策略处理
        if (placement == PlacementStrategy.LEFT_PLACEMENT) {
            handleLeftPlacement(workingCopy, routableAtNode, gateway, leftBranch, rightBranch, originalNextId);
        } else {
            handleNoMove(workingCopy, routableAtNode, gateway, leftBranch, rightBranch, originalNextId);
        }
        
        log.debug("在节点 {} 后新增网关 {}，策略: {}", atNodeId, gateway.getId(), placement);
        
        return gateway;
    }
    
    /**
     * 处理左侧放置策略
     * 根据设计方案：在网关中保持相对最上级网关为nextId=99
     */
    private void handleLeftPlacement(ProcessNode processNode, IRoutable atNode, GatewayNodeCanvas gateway,
                                   ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch, String originalNextId) {
        connectNodeToGateway(atNode, gateway);

        if (isEndOwnerConnection(originalNextId)) {
            handleLeftPlacementWithEndOwner(gateway, leftBranch, rightBranch);
        } else if (hasDownstream(originalNextId)) {
            handleLeftPlacementWithDownstream(processNode, gateway, leftBranch, rightBranch, originalNextId);
        } else {
            handleLeftPlacementEmpty(gateway, leftBranch, rightBranch);
        }
    }

    /**
     * 连接节点到网关
     */
    private void connectNodeToGateway(IRoutable atNode, GatewayNodeCanvas gateway) {
        atNode.setNextId(gateway.getId());
        gateway.setPrveId(atNode.getId());
        nodeBatchExecutor.updateNode(atNode.getId(), (BaseNodeCanvas) atNode);
    }

    /**
     * 处理左侧放置且原节点指向结束的情况
     */
    private void handleLeftPlacementWithEndOwner(GatewayNodeCanvas gateway,
                                               ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch) {
        leftBranch.setNextId("");
        rightBranch.setNextId("");
        gateway.setNextId(EndOwnerManager.END_OWNER_ID);
        log.debug("左侧放置：网关 {} 成为EndOwner载体", gateway.getId());
    }

    /**
     * 处理左侧放置且有下游节点的情况
     */
    private void handleLeftPlacementWithDownstream(ProcessNode processNode, GatewayNodeCanvas gateway,
                                                 ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch,
                                                 String originalNextId) {
        leftBranch.setNextId(originalNextId);
        updateDownstreamPrevious(processNode, originalNextId, leftBranch.getId());

        String branchTailNext = findBranchTailNext(processNode, originalNextId);
        gateway.setNextId(branchTailNext);
        rightBranch.setNextId("");

        log.debug("左侧放置：移动原链段到左分支，网关合流到 {}", branchTailNext);
    }

    /**
     * 处理左侧放置且无下游的情况
     */
    private void handleLeftPlacementEmpty(GatewayNodeCanvas gateway,
                                        ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch) {
        leftBranch.setNextId("");
        rightBranch.setNextId("");
        gateway.setNextId("");
    }

    /**
     * 检查是否为EndOwner连接
     */
    private boolean isEndOwnerConnection(String nextId) {
        return EndOwnerManager.END_OWNER_ID.equals(nextId);
    }

    /**
     * 检查是否有下游节点
     */
    private boolean hasDownstream(String nextId) {
        return nextId != null && !nextId.trim().isEmpty();
    }

    /**
     * 更新下游节点的前驱
     */
    private void updateDownstreamPrevious(ProcessNode processNode, String downstreamId, String newPreviousId) {
        BaseNodeCanvas downstream = processNode.getFlowNodeMap().get(downstreamId);
        if (downstream instanceof IRoutable routableNext) {
            routableNext.setPrveId(newPreviousId);
        }
    }
    
    /**
     * 处理不移动策略
     * 根据设计方案：在网关中保持EndOwner在网关层级
     */
    private void handleNoMove(ProcessNode processNode, IRoutable atNode, GatewayNodeCanvas gateway,
                            ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch, String originalNextId) {
        connectNodeToGateway(atNode, gateway);

        if (isEndOwnerConnection(originalNextId)) {
            handleNoMoveWithEndOwner(gateway);
        } else if (hasDownstream(originalNextId)) {
            handleNoMoveWithDownstream(processNode, gateway, originalNextId);
        }

        // 两个分支都设为空分支
        leftBranch.setNextId("");
        rightBranch.setNextId("");
    }

    /**
     * 处理不移动策略且原节点指向结束的情况
     */
    private void handleNoMoveWithEndOwner(GatewayNodeCanvas gateway) {
        gateway.setNextId(EndOwnerManager.END_OWNER_ID);
        log.debug("不移动策略：网关 {} 连接到结束", gateway.getId());
    }

    /**
     * 处理不移动策略且有下游节点的情况
     */
    private void handleNoMoveWithDownstream(ProcessNode processNode, GatewayNodeCanvas gateway, String originalNextId) {
        gateway.setNextId(originalNextId);
        updateDownstreamPrevious(processNode, originalNextId, gateway.getId());
        log.debug("不移动策略：网关 {} 合流到 {}", gateway.getId(), originalNextId);
    }
    
    /**
     * 查找分支尾部的下一个节点
     */
    private String findBranchTailNext(ProcessNode processNode, String startNodeId) {
        Set<String> visited = new HashSet<>();
        return findBranchTailNextRecursive(processNode, startNodeId, visited);
    }
    
    private String findBranchTailNextRecursive(ProcessNode processNode, String currentNodeId, Set<String> visited) {
        if (currentNodeId == null || visited.contains(currentNodeId)) {
            return "";
        }
        
        visited.add(currentNodeId);
        BaseNodeCanvas currentNode = processNode.getFlowNodeMap().get(currentNodeId);
        
        if (currentNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (nextId == null || nextId.trim().isEmpty() || EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                return nextId;
            }
            return findBranchTailNextRecursive(processNode, nextId, visited);
        }
        
        return "";
    }
    
    /**
     * 删除网关
     * @param gatewayId 网关ID
     */
    public void deleteGateway(String gatewayId) {
        if (nodeBatchExecutor == null || gatewayId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            throw new IllegalStateException("网关没有分支，无法删除");
        }

        if (flowIds.size() > 1) {
            throw new IllegalStateException("网关有多个分支，请先删除分支至只剩1条");
        }

        // 只剩一条分支，执行扁平化
        String remainingBranchId = flowIds.get(0);
        flattenGateway(workingCopy, gateway, remainingBranchId);

        nodeBatchExecutor.deleteNode(gatewayId);

        log.debug("删除网关 {}，已扁平化", gatewayId);
    }
    
    /**
     * 扁平化网关（将单分支网关替换为分支链）
     * 根据设计方案：触发AbortEndOwnerIfFlatten检查
     */
    private void flattenGateway(ProcessNode processNode, GatewayNodeCanvas gateway, String branchLeafId) {
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas branchLeaf = workingCopy.getFlowNodeMap().get(branchLeafId);

        if (branchLeaf instanceof IRoutable routableLeaf) {
            fixBranchLeafConnection(gateway, branchLeafId, routableLeaf, workingCopy);
        }

        List<BaseNodeCanvas> branchChain = WorkflowQueryService.findBranchChain(workingCopy, branchLeafId);

        if (branchChain.isEmpty()) {
            handleEmptyBranchFlatten(processNode, gateway);
        } else {
            handleNonEmptyBranchFlatten(workingCopy, gateway, branchChain, branchLeafId);
        }

        // 触发EndOwner检查
        autoWireStrategy.abortEndOwnerIfFlatten(workingCopy);
    }

    /**
     * 修复分支叶子的连接
     */
    private void fixBranchLeafConnection(GatewayNodeCanvas gateway, String branchLeafId,
                                       IRoutable routableLeaf, ProcessNode workingCopy) {
        String leafNextId = routableLeaf.getNextId();
        String gatewayNextId = gateway.getNextId();

        log.debug("修复分支叶子 {} 连接，当前nextId: {}", branchLeafId, leafNextId);

        if (gateway.getId().equals(leafNextId) || isEmptyConnection(leafNextId)) {
            connectBranchLeafToGatewayDownstream(routableLeaf, gatewayNextId, branchLeafId);

            if (isEmptyConnection(leafNextId)) {
                fixBranchLeafPrevious(gateway, routableLeaf, branchLeafId, workingCopy);
            }

            nodeBatchExecutor.updateNode(branchLeafId, (BaseNodeCanvas) routableLeaf);
        }
    }

    /**
     * 连接分支叶子到网关下游
     */
    private void connectBranchLeafToGatewayDownstream(IRoutable routableLeaf, String gatewayNextId, String branchLeafId) {
        if (isEndOwnerConnection(gatewayNextId)) {
            routableLeaf.setNextId(EndOwnerManager.END_OWNER_ID);
            log.debug("分支叶子 {} 成为新的EndOwner", branchLeafId);
        } else {
            routableLeaf.setNextId(gatewayNextId);
            log.debug("分支叶子 {} 连接到网关下游: {}", branchLeafId, gatewayNextId);
        }
    }

    /**
     * 修复分支叶子的前驱关系
     */
    private void fixBranchLeafPrevious(GatewayNodeCanvas gateway, IRoutable routableLeaf,
                                     String branchLeafId, ProcessNode workingCopy) {
        String gatewayPrevId = gateway.getPrveId();
        if (hasDownstream(gatewayPrevId)) {
            routableLeaf.setPrveId(gatewayPrevId);
            updateGatewayPreviousToPointToBranch(workingCopy, gatewayPrevId, branchLeafId);
            log.debug("分支叶子 {} 前驱设置为网关前驱: {}", branchLeafId, gatewayPrevId);
        }
    }

    /**
     * 更新网关前驱节点指向分支叶子
     */
    private void updateGatewayPreviousToPointToBranch(ProcessNode workingCopy, String gatewayPrevId, String branchLeafId) {
        BaseNodeCanvas gatewayPrev = workingCopy.getFlowNodeMap().get(gatewayPrevId);
        if (gatewayPrev instanceof IRoutable routablePrev) {
            routablePrev.setNextId(branchLeafId);
            nodeBatchExecutor.updateNode(gatewayPrevId, gatewayPrev);
        }
    }

    /**
     * 检查是否为空连接
     */
    private boolean isEmptyConnection(String nextId) {
        return nextId == null || nextId.trim().isEmpty();
    }

    /**
     * 处理空分支的扁平化
     */
    private void handleEmptyBranchFlatten(ProcessNode processNode, GatewayNodeCanvas gateway) {
        log.debug("扁平化：分支链为空，直接连接前驱到后继");
        String gatewayNextId = gateway.getNextId();

        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, gateway.getId());
        for (BaseNodeCanvas prevNode : prevNodes) {
            if (prevNode instanceof IRoutable routablePrev) {
                if (isEndOwnerConnection(gatewayNextId)) {
                    autoWireStrategy.connectToEnd(processNode, prevNode);
                } else {
                    routablePrev.setNextId(gatewayNextId);
                    updateDownstreamIfExists(processNode, gatewayNextId, prevNode.getId());
                }
                nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
            }
        }
    }

    /**
     * 处理非空分支的扁平化
     */
    private void handleNonEmptyBranchFlatten(ProcessNode workingCopy, GatewayNodeCanvas gateway,
                                           List<BaseNodeCanvas> branchChain, String branchLeafId) {
        BaseNodeCanvas branchHead = branchChain.get(0);
        BaseNodeCanvas branchTail = branchChain.get(branchChain.size() - 1);

        log.debug("扁平化网关 {}，分支链: {} -> {}，链长度: {}",
                 gateway.getId(), branchHead.getId(), branchTail.getId(), branchChain.size());

        connectPreviousNodesToBranchHead(workingCopy, gateway, branchHead);
        updateBranchChainNodes(branchChain, branchLeafId);
        connectBranchTailToGatewayDownstream(workingCopy, gateway, branchTail);
    }

    /**
     * 更新下游节点（如果存在）
     */
    private void updateDownstreamIfExists(ProcessNode processNode, String downstreamId, String newPreviousId) {
        if (hasDownstream(downstreamId)) {
            BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(downstreamId);
            if (nextNode instanceof IRoutable routableNext) {
                routableNext.setPrveId(newPreviousId);
                nodeBatchExecutor.updateNode(nextNode.getId(), nextNode);
            }
        }
    }

    /**
     * 连接前驱节点到分支头
     */
    private void connectPreviousNodesToBranchHead(ProcessNode workingCopy, GatewayNodeCanvas gateway, BaseNodeCanvas branchHead) {
        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(workingCopy, gateway.getId());
        for (BaseNodeCanvas prevNode : prevNodes) {
            if (prevNode instanceof IRoutable routablePrev) {
                routablePrev.setNextId(branchHead.getId());
                nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
            }
        }

        // 更新分支头的前驱
        if (branchHead instanceof IRoutable routableHead && !prevNodes.isEmpty()) {
            routableHead.setPrveId(prevNodes.get(0).getId());
            nodeBatchExecutor.updateNode(branchHead.getId(), branchHead);
        }
    }

    /**
     * 更新分支链中的所有节点
     */
    private void updateBranchChainNodes(List<BaseNodeCanvas> branchChain, String branchLeafId) {
        for (BaseNodeCanvas chainNode : branchChain) {
            if (!chainNode.getId().equals(branchLeafId)) {
                nodeBatchExecutor.updateNode(chainNode.getId(), chainNode);
                log.debug("更新分支链节点: {}", chainNode.getId());
            }
        }
    }

    /**
     * 连接分支尾到网关下游
     */
    private void connectBranchTailToGatewayDownstream(ProcessNode workingCopy, GatewayNodeCanvas gateway, BaseNodeCanvas branchTail) {
            // 分支不为空，用分支链替换网关
            BaseNodeCanvas branchHead = branchChain.get(0);
            BaseNodeCanvas branchTail = branchChain.get(branchChain.size() - 1);

            log.info("扁平化网关 {}，分支链: {} -> {}，链长度: {}", gateway.getId(), branchHead.getId(), branchTail.getId(), branchChain.size());

            // 手动处理网关替换：连接前驱到分支头，分支尾到下游
            List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, gateway.getId());
            for (BaseNodeCanvas prevNode : prevNodes) {
                if (prevNode instanceof IRoutable routablePrev) {
                    routablePrev.setNextId(branchHead.getId());
                    // 重要：记录变更到NodeBatchExecutor
                    nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
                }
            }

            // 更新分支头的前驱
            if (branchHead instanceof IRoutable routableHead) {
                if (!prevNodes.isEmpty()) {
                    routableHead.setPrveId(prevNodes.get(0).getId());
                    // 重要：记录变更到NodeBatchExecutor
                    nodeBatchExecutor.updateNode(branchHead.getId(), branchHead);
                }
            }

            // 更新分支链中所有节点到NodeBatchExecutor
            // 注意：跳过分支叶子，因为它可能已经在前面被修改过了
            for (BaseNodeCanvas chainNode : branchChain) {
                if (!chainNode.getId().equals(branchLeafId)) {
                    // 只更新非分支叶子的节点，避免覆盖之前的修改
                    nodeBatchExecutor.updateNode(chainNode.getId(), chainNode);
                    log.debug("更新分支链节点: {}", chainNode.getId());
                } else {
                    log.debug("跳过分支叶子节点 {}，避免覆盖之前的修改", chainNode.getId());
                }
            }

            // 如果分支尾的nextId为空（等待合流），需要处理
            // 重新从工作副本获取分支尾部的最新状态
            BaseNodeCanvas latestBranchTail = workingCopy.getFlowNodeMap().get(branchTail.getId());
            if (latestBranchTail instanceof IRoutable routableTail) {
                String branchTailNext = routableTail.getNextId();
                log.info("扁平化：分支尾部 {} 的最新nextId: {}", branchTail.getId(), branchTailNext);
                if (branchTailNext == null || branchTailNext.trim().isEmpty()) {
                    // 分支尾原来等待合流，现在需要连接到网关的下游
                    // 这个逻辑在下面的gatewayNextId处理中会设置
                    log.info("扁平化：分支尾部为空，需要在下面处理");
                } else {
                    log.info("扁平化：分支尾部不为空，nextId: {}，需要重新连接前驱", branchTailNext);

                    // 查找所有指向网关的节点，将它们连接到分支头
                    List<BaseNodeCanvas> gatewayPrevNodes = WorkflowQueryService.findPrevNodes(workingCopy, gateway.getId());
                    log.info("扁平化：找到 {} 个节点指向网关", gatewayPrevNodes.size());

                    // 调试：检查所有节点的nextId
                    log.info("调试：工作副本中共有 {} 个节点", workingCopy.getFlowNodeMap().size());
                    for (BaseNodeCanvas node : workingCopy.getFlowNodeMap().values()) {
                        if (node instanceof IRoutable routableNode) {
                            String nextId = routableNode.getNextId();
                            log.info("调试：节点 {} (类型: {}) 的nextId: {}", node.getId(), node.getClass().getSimpleName(), nextId);
                            if (gateway.getId().equals(nextId)) {
                                log.info("调试：找到指向网关的节点: {} -> {}", node.getId(), gateway.getId());
                                gatewayPrevNodes.add(node); // 手动添加到列表中
                            }
                        }
                    }

                    // 处理所有指向网关的节点
                    for (BaseNodeCanvas prevNode : gatewayPrevNodes) {
                        if (prevNode instanceof IRoutable routablePrev) {
                            log.info("扁平化：将前驱节点 {} 从网关 {} 重新连接到分支头 {}",
                                    prevNode.getId(), gateway.getId(), branchHead.getId());
                            routablePrev.setNextId(branchHead.getId());
                            nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
                        }
                    }

                    // 额外检查：确保所有已经指向分支头的节点也被保存到NodeBatchExecutor
                    BaseNodeCanvas actualPrevNode = null;
                    for (BaseNodeCanvas node : workingCopy.getFlowNodeMap().values()) {
                        if (node instanceof IRoutable routableNode) {
                            String nextId = routableNode.getNextId();
                            if (branchHead.getId().equals(nextId)) {
                                log.info("扁平化：保存已连接到分支头的节点 {} 到NodeBatchExecutor，节点nextId: {}",
                                        node.getId(), nextId);
                                nodeBatchExecutor.updateNode(node.getId(), node);

                                // 验证更新后的状态
                                ProcessNode updatedWorkingCopy = nodeBatchExecutor.getWorkingCopy();
                                BaseNodeCanvas updatedNode = updatedWorkingCopy.getFlowNodeMap().get(node.getId());
                                if (updatedNode instanceof IRoutable updatedRoutable) {
                                    log.info("扁平化：更新后工作副本中节点 {} 的nextId: {}",
                                            node.getId(), updatedRoutable.getNextId());
                                }

                                if (actualPrevNode == null) {
                                    actualPrevNode = node; // 记录第一个前驱节点
                                }
                            }
                        }
                    }

                    // 如果找到了实际的前驱节点，设置分支头的前驱
                    if (actualPrevNode != null && branchHead instanceof IRoutable routableHead) {
                        routableHead.setPrveId(actualPrevNode.getId());
                        nodeBatchExecutor.updateNode(branchHead.getId(), branchHead);
                        log.info("扁平化：分支头 {} 的前驱设置为实际前驱 {}", branchHead.getId(), actualPrevNode.getId());
                    }

                    // 更新分支头的前驱
                    if (branchHead instanceof IRoutable routableHead && !gatewayPrevNodes.isEmpty()) {
                        routableHead.setPrveId(gatewayPrevNodes.get(0).getId());
                        nodeBatchExecutor.updateNode(branchHead.getId(), branchHead);
                        log.info("扁平化：分支头 {} 的前驱设置为 {}", branchHead.getId(), gatewayPrevNodes.get(0).getId());
                    }

                    // 直接跳到最后的EndOwner检查
                    log.debug("网关 {} 扁平化完成，保留分支链", gateway.getId());
                    // 注意：在扁平化情况下，不调用abortEndOwnerIfFlatten，因为我们已经正确设置了连接
                    return;
                }
            }

            // 处理网关原来的下游连接
            // 重新获取网关对象，确保获取到最新的状态
            GatewayNodeCanvas latestGateway = WorkflowQueryService.findGateway(workingCopy, gateway.getId());
            String gatewayNextId = latestGateway != null ? latestGateway.getNextId() : gateway.getNextId();
            log.debug("扁平化：网关 {} 的最新nextId: {}", gateway.getId(), gatewayNextId);
            if (branchTail instanceof IRoutable routableTail) {
                if (EndOwnerManager.END_OWNER_ID.equals(gatewayNextId)) {
                    // 网关原来指向结束，分支尾部应该成为EndOwner
                    // 检查分支尾部是否已经是EndOwner
                    if (!EndOwnerManager.END_OWNER_ID.equals(routableTail.getNextId())) {
                        // 分支尾部还不是EndOwner，设置为EndOwner
                        routableTail.setNextId(EndOwnerManager.END_OWNER_ID);
                        log.debug("扁平化：分支尾部 {} 成为EndOwner", branchTail.getId());
                        // 重要：记录变更到NodeBatchExecutor
                        nodeBatchExecutor.updateNode(branchTail.getId(), branchTail);
                    } else {
                        log.debug("扁平化：分支尾部 {} 已经是EndOwner，无需修改", branchTail.getId());
                    }
                } else if (gatewayNextId != null && !gatewayNextId.trim().isEmpty()) {
                    // 分支尾部连接到网关的原下游
                    routableTail.setNextId(gatewayNextId);
                    // 重要：记录变更到NodeBatchExecutor
                    nodeBatchExecutor.updateNode(branchTail.getId(), branchTail);
                    // 更新下游节点的前驱
                    BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(gatewayNextId);
                    if (nextNode instanceof IRoutable routableNext) {
                        routableNext.setPrveId(branchTail.getId());
                        // 重要：记录变更到NodeBatchExecutor
                        nodeBatchExecutor.updateNode(nextNode.getId(), nextNode);
                    }
                } else {
                    // 网关原来没有下游，分支尾部也设为空
                    routableTail.setNextId("");
                    // 重要：记录变更到NodeBatchExecutor
                    nodeBatchExecutor.updateNode(branchTail.getId(), branchTail);
                }
            }

            log.debug("网关 {} 扁平化完成，保留分支链", gateway.getId());
        }

        // 触发EndOwner检查（根据设计方案的AbortEndOwnerIfFlatten）
        autoWireStrategy.abortEndOwnerIfFlatten(workingCopy);
    }
    
    /**
     * 修改网关类型
     * @param gatewayId 网关ID
     * @param toType 目标类型
     */
    public void switchGatewayType(String gatewayId, Integer toType) {
        if (nodeBatchExecutor == null || gatewayId == null || toType == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        // 使用网关语义策略处理类型切换
        GatewaySemanticsStrategy.switchGatewayType(workingCopy, gateway, toType);

        // 更新网关节点
        nodeBatchExecutor.updateNode(gatewayId, gateway);

        // 更新所有被修改的分支节点
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds != null) {
            for (String flowId : flowIds) {
                BaseNodeCanvas branchNode = workingCopy.getFlowNodeMap().get(flowId);
                if (branchNode != null) {
                    nodeBatchExecutor.updateNode(flowId, branchNode);
                    log.debug("更新分支节点: {}", flowId);
                }
            }
        }

        log.debug("网关 {} 类型已切换为 {}", gatewayId, toType);
    }

    /**
     * 为分支设置默认条件
     *
     * @param branch 分支节点
     * @param index 分支索引
     */
    private void setDefaultConditionForBranch(ConditionNodeCanvas branch, int index) {
        if (branch.getOperateCondition() == null || branch.getOperateCondition().isEmpty()) {
            List<List<ConditionGroup>> defaultConditions = new ArrayList<>();
            List<ConditionGroup> conditionGroup = new ArrayList<>();

            // 创建一个简单的条件组
            ConditionGroup condition = new ConditionGroup();
            condition.setNodeId("system");  // 设置必需的 nodeId
            condition.setNodeName("系统");

            if (index == 2 - 1) {
                // 最后一个分支设为 else 条件
                condition.setFiledId("else");
                condition.setFiledValue("其他情况");
                condition.setConditionId("default_else");
                condition.setValue("else");
            } else {
                // 其他分支生成默认条件
                condition.setFiledId("condition_" + (index + 1));
                condition.setFiledValue("始终成立");
                condition.setConditionId("default_condition_" + (index + 1));
                condition.setValue("true");
            }

            conditionGroup.add(condition);
            defaultConditions.add(conditionGroup);
            branch.setOperateCondition(defaultConditions);

            log.debug("为分支 {} 生成默认条件: {}", branch.getId(), condition.getFiledId());
        }
    }
}
